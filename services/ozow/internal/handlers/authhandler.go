package handlers

import (
	"aps/services/ozow/internal/database"
	"aps/services/ozow/internal/logging"
	"aps/services/ozow/internal/models"
	"aps/services/ozow/internal/session"
	"aps/services/ozow/internal/views"
	"database/sql"
	"log"
	"net/http"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

var sessionManager *session.SessionManager
var redisLogger *logging.RedisLogger

func init() {
	sessionManager = session.NewSessionManager()
	redisLogger = logging.NewRedisLogger()
}

func LoginHandler(w http.ResponseWriter, r *http.Request) {
	if sessionManager.IsAuthenticated(r) {
		http.Redirect(w, r, "/ozow/home", http.StatusFound)
		return
	}

	err := views.Login(views.LoginData{Error: ""}).Render(r.Context(), w)
	if err != nil {
		log.Printf("Error rendering login page: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

func LoginPostHandler(w http.ResponseWriter, r *http.Request) {
	idNumber := strings.TrimSpace(r.FormValue("idNumber"))
	password := r.FormValue("password")

	if idNumber == "" || password == "" {
		errorMsg := "Please enter both ID number and password"
		if r.Header.Get("HX-Request") == "true" {
			err := views.LoginForm(views.LoginData{Error: errorMsg}).Render(r.Context(), w)
			if err != nil {
				log.Printf("Error rendering login form: %v", err)
				http.Error(w, "Internal server error", http.StatusInternalServerError)
			}
			return
		}
		err := views.Login(views.LoginData{Error: errorMsg}).Render(r.Context(), w)
		if err != nil {
			log.Printf("Error rendering login page: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}

	user, err := AuthenticateUser(idNumber, password)
	if err != nil {
		log.Printf("Error authenticating user: %v", err)
		redisLogger.LogLoginAttempt(idNumber, false, r, "Database error")
		errorMsg := "Internal server error"
		if r.Header.Get("HX-Request") == "true" {
			err := views.LoginForm(views.LoginData{Error: errorMsg}).Render(r.Context(), w)
			if err != nil {
				log.Printf("Error rendering login form: %v", err)
				http.Error(w, "Internal server error", http.StatusInternalServerError)
			}
			return
		}
		err = views.Login(views.LoginData{Error: errorMsg}).Render(r.Context(), w)
		if err != nil {
			log.Printf("Error rendering login page: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}

	if user == nil {
		redisLogger.LogLoginAttempt(idNumber, false, r, "Invalid credentials")
		errorMsg := "Invalid ID number or password"
		if r.Header.Get("HX-Request") == "true" {
			err := views.LoginForm(views.LoginData{Error: errorMsg}).Render(r.Context(), w)
			if err != nil {
				log.Printf("Error rendering login form: %v", err)
				http.Error(w, "Internal server error", http.StatusInternalServerError)
			}
			return
		}
		err := views.Login(views.LoginData{Error: errorMsg}).Render(r.Context(), w)
		if err != nil {
			log.Printf("Error rendering login page: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}

	if err := sessionManager.CreateSession(w, user.ID); err != nil {
		log.Printf("Error creating session: %v", err)
		redisLogger.LogLoginAttempt(idNumber, false, r, "Session creation failed")
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Log successful login
	redisLogger.LogLoginAttempt(idNumber, true, r, "")

	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/ozow/home")
		w.WriteHeader(http.StatusOK)
		return
	}

	http.Redirect(w, r, "/ozow/home", http.StatusFound)
}

func LogoutHandler(w http.ResponseWriter, r *http.Request) {
	if err := sessionManager.DeleteSession(w, r); err != nil {
		log.Printf("Error deleting session on logout: %v", err)
	}

	if r.Header.Get("HX-Request") == "true" {
		w.Header().Set("HX-Redirect", "/ozow/login")
		w.WriteHeader(http.StatusOK)
		return
	}

	http.Redirect(w, r, "/ozow/login", http.StatusFound)
}

func GetCurrentUser(r *http.Request) (*models.User, error) {
	userID, err := sessionManager.GetUserID(r)
	if err != nil {
		return nil, err
	}
	if userID == "" {
		return nil, nil
	}

	return GetUserByID(userID)
}

func RequireAuth(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if !sessionManager.IsAuthenticated(r) {
			if r.Header.Get("HX-Request") == "true" {
				w.Header().Set("HX-Redirect", "/ozow/login")
				w.WriteHeader(http.StatusUnauthorized)
				return
			}
			http.Redirect(w, r, "/ozow/login", http.StatusSeeOther)
			return
		}
		next(w, r)
	}
}

func GetUserByID(idNumber string) (*models.User, error) {
	if strings.TrimSpace(idNumber) == "" {
		return nil, nil
	}

	db, err := database.GetDBConnection()
	if err != nil {
		log.Printf("Error connecting to database: %v", err)
		return nil, err
	}
	defer db.Close()

	query := `
		SELECT
			contract_key, identification_detail, employer,
			first_name, surname, salary_amount
		FROM form_data
		WHERE identification_detail = ?
		LIMIT 1
	`

	var clientKey, identificationDetail, employer sql.NullString
	var firstName, surname sql.NullString
	var salaryAmount sql.NullFloat64

	err = db.QueryRow(query, idNumber).Scan(
		&clientKey, &identificationDetail, &employer,
		&firstName, &surname, &salaryAmount,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		log.Printf("Error querying user: %v", err)
		return nil, err
	}

	user := &models.User{
		ID:                   idNumber,
		IdentificationDetail: identificationDetail.String,
		FirstName:            firstName.String,
		Surname:              surname.String,
		FullName:             strings.TrimSpace(firstName.String + " " + surname.String),
		Employer:             employer.String,
		Department:           "", // Not available in database
		EmployeeCode:         "", // Not available in database
		SalaryAmount:         salaryAmount.Float64,
		ClientKey:            clientKey.String,
	}

	return user, nil
}

func AuthenticateUser(idNumber, password string) (*models.User, error) {
	if strings.TrimSpace(idNumber) == "" || password == "" {
		return nil, nil
	}

	db, err := database.GetDBConnection()
	if err != nil {
		log.Printf("Error connecting to database: %v", err)
		return nil, err
	}
	defer db.Close()

	// Get the user and their password hash
	query := `
		SELECT
			contract_key, identification_detail, employer,
			first_name, surname, salary_amount, contract_hash
		FROM form_data
		WHERE identification_detail = ?
		LIMIT 1
	`

	var clientKey, identificationDetail, employer sql.NullString
	var firstName, surname, passwordHash sql.NullString
	var salaryAmount sql.NullFloat64

	err = db.QueryRow(query, idNumber).Scan(
		&clientKey, &identificationDetail, &employer,
		&firstName, &surname, &salaryAmount, &passwordHash,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		log.Printf("Error querying user: %v", err)
		return nil, err
	}

	// Check if password matches the contract_hash
	if !passwordHash.Valid || passwordHash.String != password {
		return nil, nil
	}

	user := &models.User{
		ID:                   idNumber,
		IdentificationDetail: identificationDetail.String,
		FirstName:            firstName.String,
		Surname:              surname.String,
		FullName:             strings.TrimSpace(firstName.String + " " + surname.String),
		Employer:             employer.String,
		Department:           "", // Not available in database
		EmployeeCode:         "", // Not available in database
		SalaryAmount:         salaryAmount.Float64,
		ClientKey:            clientKey.String,
	}

	return user, nil
}

func SecureAuthenticateUser(idNumber, password string) (*models.User, error) {
	if strings.TrimSpace(idNumber) == "" || password == "" {
		return nil, nil
	}

	db, err := database.GetDBConnection()
	if err != nil {
		log.Printf("Error connecting to database: %v", err)
		return nil, err
	}
	defer db.Close()

	// First get the user and their password hash
	query := `
		SELECT
			contract_key, identification_detail, employer,
			first_name, surname, salary_amount, contract_hash
		FROM form_data
		WHERE identification_detail = ?
		LIMIT 1
	`

	var clientKey, identificationDetail, employer sql.NullString
	var firstName, surname, passwordHash sql.NullString
	var salaryAmount sql.NullFloat64

	err = db.QueryRow(query, idNumber).Scan(
		&clientKey, &identificationDetail, &employer,
		&firstName, &surname, &salaryAmount, &passwordHash,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		log.Printf("Error querying user: %v", err)
		return nil, err
	}

	// Check password hash
	if !passwordHash.Valid || !CheckPasswordHash(password, passwordHash.String) {
		return nil, nil
	}

	user := &models.User{
		ID:                   idNumber,
		IdentificationDetail: identificationDetail.String,
		FirstName:            firstName.String,
		Surname:              surname.String,
		FullName:             strings.TrimSpace(firstName.String + " " + surname.String),
		Employer:             employer.String,
		Department:           "", // Not available in database
		EmployeeCode:         "", // Not available in database
		SalaryAmount:         salaryAmount.Float64,
		ClientKey:            clientKey.String,
	}

	return user, nil
}

func HashPassword(password string) (string, error) {
	if password == "" {
		return "", nil
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

func CheckPasswordHash(password, hash string) bool {
	if password == "" || hash == "" {
		return false
	}

	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}
