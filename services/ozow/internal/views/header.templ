package views

import "aps/services/ozow/internal/models"

templ Header(page string) {
	<header class="navbar bg-white border-b border-gray-200 px-4 py-3">
		<div class="navbar-start">
			<div class="flex items-center space-x-3">
				<div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
					<span class="text-white font-bold text-sm">💳</span>
				</div>
				<div>
					<h1 class="text-xl font-semibold text-gray-900">Ozow Payment</h1>
					<p class="text-xs text-gray-500">Secure Payment Portal</p>
				</div>
			</div>
		</div>
		<div class="navbar-end">
			<a class="btn btn-primary shadow-md hover:shadow-lg transition-shadow" href="/ozow/login">
				<span class="text-sm">🔐</span>
				Login
			</a>
		</div>
	</header>
}

templ HeaderWithUser(page string, user *models.User) {
	<header class="navbar bg-white border-b border-gray-200 px-4 py-3">
		<div class="navbar-start">
			<div class="flex items-center space-x-3">
				<div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
					<span class="text-white font-bold text-sm">💳</span>
				</div>
				<div>
					<h1 class="text-xl font-semibold text-gray-900">Ozow Payment</h1>
					<p class="text-xs text-gray-500">Secure Payment Portal</p>
				</div>
			</div>
		</div>

		<div class="navbar-center hidden lg:flex">
			<div class="bg-green-50 border border-green-200 rounded-full px-4 py-2">
				<div class="flex items-center space-x-2">
					<div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
					<span class="text-sm font-medium text-green-700">Secure Session Active</span>
				</div>
			</div>
		</div>

		<div class="navbar-end">
			<div class="flex items-center space-x-4">
				<!-- User Info -->
				<div class="hidden md:flex flex-col items-end">
					<span class="text-sm font-medium text-gray-900">{ user.FirstName } { user.Surname }</span>
					<span class="text-xs text-gray-500">ID: { user.IdentificationDetail }</span>
				</div>

				<!-- User Menu Dropdown -->
				<div class="dropdown dropdown-end">
					<div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar hover:bg-gray-100">
						<div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-md">
							<span class="text-white font-bold text-sm">
								{ string([]rune(user.FirstName)[0]) }{ string([]rune(user.Surname)[0]) }
							</span>
						</div>
					</div>
					<ul tabindex="0" class="mt-3 z-50 p-2 shadow-xl menu menu-sm dropdown-content bg-white rounded-xl border border-gray-200 w-56">
						<li class="menu-title">
							<span class="text-gray-500 font-medium">Account</span>
						</li>
						<li>
							<a class="flex items-center space-x-3 py-3 px-3 hover:bg-gray-50 rounded-lg">
								<div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
									<span class="text-blue-600 text-sm">👤</span>
								</div>
								<div>
									<span class="font-medium">Profile</span>
									<span class="text-xs text-gray-500 block">View account details</span>
								</div>
							</a>
						</li>
						<li>
							<a class="flex items-center space-x-3 py-3 px-3 hover:bg-gray-50 rounded-lg">
								<div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
									<span class="text-purple-600 text-sm">📊</span>
								</div>
								<div>
									<span class="font-medium">Payment History</span>
									<span class="text-xs text-gray-500 block">View past transactions</span>
								</div>
							</a>
						</li>
						<li>
							<a class="flex items-center space-x-3 py-3 px-3 hover:bg-gray-50 rounded-lg">
								<div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
									<span class="text-gray-600 text-sm">⚙️</span>
								</div>
								<div>
									<span class="font-medium">Settings</span>
									<span class="text-xs text-gray-500 block">Account preferences</span>
								</div>
							</a>
						</li>
						<div class="divider my-2"></div>
						<li>
							<button
								hx-post="/ozow/logout"
								hx-confirm="Are you sure you want to logout?"
								class="flex items-center space-x-3 py-3 px-3 hover:bg-red-50 rounded-lg text-red-600 w-full"
							>
								<div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
									<span class="text-red-600 text-sm">🚪</span>
								</div>
								<div class="text-left">
									<span class="font-medium">Logout</span>
									<span class="text-xs text-red-500 block">End secure session</span>
								</div>
							</button>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</header>
}