// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.906
package views

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/models"
)

const (
	page         = "Pay"
	style        = "/ozow/css/style.css"
	payment_link = "/ozow/pay"
)

func Index() templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<!doctype html><html lang=\"en\" data-theme=\"light\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Pay</title><link rel=\"stylesheet\" type=\"text/css\" href=\"")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var2 string
		templ_7745c5c3_Var2, templ_7745c5c3_Err = templ.JoinStringErrs(models.STYLE)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 24, Col: 23}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var2))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, "\"><link rel=\"icon\" href=\"data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>\"></head><body><script type=\"module\" src=\"")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var3 string
		templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(models.JS)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 29, Col: 40}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, "\"></script>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = components.Header(page).Render(ctx, templ_7745c5c3_Buffer)
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, "<div class=\"flex min-h-screen items-center justify-center bg-opacity-80\"><div class=\"card glass w-96 pt-6\"><div class=\"items-center justify-center  text-center text-3xl\">🪙</div><div class=\"card-body grid place-items-center\"><form id=\"payment-form\" hx-post=\"")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var4 string
		templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(payment_link)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 39, Col: 29}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 5, "\" hx-target=\"#toast\" hx-indicator=\"#indicator\" hx-swap=\"innerHTML\" class=\"grid place-items-center\"><div id=\"toast\" class=\"card-body place-items-center w-80\"><h2 class=\"card-title pb-3\">Make Payment</h2><input name=\"idNumber\" id=\"idNumber\" type=\"text\" placeholder=\"ID Number\" class=\"input focus:outline-none\"> <input name=\"amount\" id=\"amount\" type=\"number\" placeholder=\"Amount\" class=\"input focus:outline-none\"></div><button type=\"submit\" class=\"content-center btn btn-primary btn-wide\">Pay</button> <span id=\"indicator\" class=\"icon-[line-md--loading-twotone-loop] text-2xl htmx-indicator\"></span></form></div></div></div></body></html>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

func IndexWithUser(user *models.User) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var5 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var5 == nil {
			templ_7745c5c3_Var5 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 6, "<!doctype html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Pay - Payment Interface</title><style>\r\n\t\t\t\t* {\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\tpadding: 0;\r\n\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tbody {\r\n\t\t\t\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\r\n\t\t\t\t\tbackground: #f5f5f7;\r\n\t\t\t\t\tmin-height: 100vh;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Header */\r\n\t\t\t\t.header {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding: 16px 32px;\r\n\t\t\t\t\tbackground: white;\r\n\t\t\t\t\tborder-bottom: 1px solid #e5e7eb;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\tfont-size: 24px;\r\n\t\t\t\t\tfont-weight: 700;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.welcome {\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-controls {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tgap: 4px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-controls button {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tgap: 6px;\r\n\t\t\t\t\tpadding: 10px 14px;\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\ttransition: all 0.2s;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-controls button:hover {\r\n\t\t\t\t\tbackground: #f3f4f6;\r\n\t\t\t\t\tcolor: #374151;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-controls .logout:hover {\r\n\t\t\t\t\tbackground: #fef2f2;\r\n\t\t\t\t\tcolor: #dc2626;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.icon {\r\n\t\t\t\t\twidth: 16px;\r\n\t\t\t\t\theight: 16px;\r\n\t\t\t\t\tfill: currentColor;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Main Content */\r\n\t\t\t\t.main-content {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding: 80px 32px;\r\n\t\t\t\t\tmax-width: 500px;\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* User Avatar */\r\n\t\t\t\t.user-avatar {\r\n\t\t\t\t\twidth: 80px;\r\n\t\t\t\t\theight: 80px;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tbackground: #6366f1;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t\tfont-size: 28px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tmargin-bottom: 16px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.welcome-title {\r\n\t\t\t\t\tfont-size: 24px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #1f2937;\r\n\t\t\t\t\tmargin-bottom: 8px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.user-subtitle {\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t\tmargin-bottom: 48px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* Payment Card */\r\n\t\t\t\t.payment-card {\r\n\t\t\t\t\tbackground: white;\r\n\t\t\t\t\tborder-radius: 12px;\r\n\t\t\t\t\tpadding: 32px;\r\n\t\t\t\t\tbox-shadow: 0 1px 3px rgba(0,0,0,0.1);\r\n\t\t\t\t\tborder: 1px solid #e5e7eb;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tmax-width: 400px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.payment-header {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tgap: 12px;\r\n\t\t\t\t\tmargin-bottom: 24px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.payment-icon {\r\n\t\t\t\t\twidth: 20px;\r\n\t\t\t\t\theight: 20px;\r\n\t\t\t\t\tfill: #6366f1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.payment-title {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #1f2937;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.form-group {\r\n\t\t\t\t\tmargin-bottom: 20px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.form-label {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #374151;\r\n\t\t\t\t\tmargin-bottom: 6px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input-wrapper {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.form-input {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tpadding: 12px 16px;\r\n\t\t\t\t\tborder: 1px solid #d1d5db;\r\n\t\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\ttransition: border-color 0.2s;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.form-input:focus {\r\n\t\t\t\t\toutline: none;\r\n\t\t\t\t\tborder-color: #6366f1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.form-input:read-only {\r\n\t\t\t\t\tbackground: #f9fafb;\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.verified-badge {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 12px;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\tbackground: #10b981;\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t\tfont-size: 11px;\r\n\t\t\t\t\tpadding: 2px 6px;\r\n\t\t\t\t\tborder-radius: 4px;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.amount-input {\r\n\t\t\t\t\tfont-size: 18px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.minimum-note {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t\tmargin-top: 4px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.button-group {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tgap: 12px;\r\n\t\t\t\t\tmargin-top: 24px;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tpadding: 14px 20px;\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t\tborder-radius: 8px;\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcursor: pointer;\r\n\t\t\t\t\ttransition: all 0.2s;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn-primary {\r\n\t\t\t\t\tbackground: #6366f1;\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn-primary:hover {\r\n\t\t\t\t\tbackground: #5855eb;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn-secondary {\r\n\t\t\t\t\tbackground: transparent;\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t\tborder: 1px solid #d1d5db;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn-secondary:hover {\r\n\t\t\t\t\tbackground: #f9fafb;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.loading-indicator {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tpadding: 20px;\r\n\t\t\t\t\tcolor: #6b7280;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.loading-indicator.show {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t@media (max-width: 768px) {\r\n\t\t\t\t\t.header {\r\n\t\t\t\t\t\tpadding: 12px 16px;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.welcome {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.main-content {\r\n\t\t\t\t\t\tpadding: 40px 16px;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.payment-card {\r\n\t\t\t\t\t\tpadding: 24px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t</style><link rel=\"icon\" href=\"data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>\"></head><body><script type=\"module\" src=\"")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var6 string
		templ_7745c5c3_Var6, templ_7745c5c3_Err = templ.JoinStringErrs(models.JS)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 336, Col: 40}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var6))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 7, "\"></script><div class=\"header\"><div class=\"logo\">Pay</div><div class=\"welcome\">Welcome, ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var7 string
		templ_7745c5c3_Var7, templ_7745c5c3_Err = templ.JoinStringErrs(user.FullName)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 340, Col: 49}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var7))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 8, "</div><div class=\"user-controls\"><button><svg class=\"icon\" viewBox=\"0 0 24 24\"><path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"></path></svg> Profile</button> <button><svg class=\"icon\" viewBox=\"0 0 24 24\"><path d=\"M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11.03L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11.03C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z\"></path></svg> Settings</button> <button class=\"logout\" hx-post=\"/ozow/logout\" hx-confirm=\"Are you sure you want to logout?\"><svg class=\"icon\" viewBox=\"0 0 24 24\"><path d=\"M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z\"></path></svg> Logout</button></div></div><div class=\"main-content\"><div class=\"user-avatar\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var8 string
		templ_7745c5c3_Var8, templ_7745c5c3_Err = templ.JoinStringErrs(string([]rune(user.FirstName)[0]))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 364, Col: 64}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var8))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var9 string
		templ_7745c5c3_Var9, templ_7745c5c3_Err = templ.JoinStringErrs(string([]rune(user.Surname)[0]))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 364, Col: 99}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var9))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 9, "</div><h1 class=\"welcome-title\">Welcome, ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var10 string
		templ_7745c5c3_Var10, templ_7745c5c3_Err = templ.JoinStringErrs(user.FirstName)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 365, Col: 55}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var10))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 10, "!</h1>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		if user.Employer != "" {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 11, "<p class=\"user-subtitle\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var11 string
			templ_7745c5c3_Var11, templ_7745c5c3_Err = templ.JoinStringErrs(user.Employer)
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 367, Col: 45}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var11))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 12, "</p>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 13, "<div class=\"payment-card\"><div class=\"payment-header\"><svg class=\"payment-icon\" viewBox=\"0 0 24 24\"><path d=\"M20,8H4V6H20M20,18H4V12H20M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.11,4 20,4Z\"></path></svg><h2 class=\"payment-title\">Make Payment</h2></div><form id=\"payment-form\" hx-post=\"")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var12 string
		templ_7745c5c3_Var12, templ_7745c5c3_Err = templ.JoinStringErrs(payment_link)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 380, Col: 28}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var12))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 14, "\" hx-target=\"#payment-result\" hx-indicator=\"#loading-indicator\" hx-swap=\"innerHTML\"><div class=\"form-group\"><label class=\"form-label\" for=\"idNumber\">ID Number ✓ Verified</label> <input type=\"text\" id=\"idNumber\" name=\"idNumber\" class=\"form-input\" value=\"")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var13 string
		templ_7745c5c3_Var13, templ_7745c5c3_Err = templ.JoinStringErrs(user.IdentificationDetail)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/webpayment.templ`, Line: 392, Col: 41}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var13))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 15, "\" readonly></div><div class=\"form-group\"><label class=\"form-label\" for=\"amount\">Payment Amount</label> <input type=\"number\" id=\"amount\" name=\"amount\" class=\"form-input amount-input\" placeholder=\"0.00\" min=\"1.00\" step=\"0.01\" required><div class=\"minimum-note\">Minimum: R1.00</div></div><div class=\"button-group\"><button type=\"button\" class=\"btn btn-secondary\" onclick=\"clearAmount()\">Clear Amount</button> <button type=\"submit\" class=\"btn btn-primary\">Process Payment</button></div></form><div id=\"loading-indicator\" class=\"loading-indicator htmx-indicator\">Processing payment...</div><div id=\"payment-result\"></div></div></div><script>\r\n\t\t\t\tfunction clearAmount() {\r\n\t\t\t\t\tdocument.getElementById('amount').value = '';\r\n\t\t\t\t}\r\n\t\t\t</script></body></html>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
