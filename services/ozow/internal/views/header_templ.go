// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.906
package views

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import "aps/services/ozow/internal/models"

func Header(page string) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<header class=\"navbar bg-white border-b border-gray-200 px-4 py-3\"><div class=\"navbar-start\"><div class=\"flex items-center space-x-3\"><div class=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\"><span class=\"text-white font-bold text-sm\">💳</span></div><div><h1 class=\"text-xl font-semibold text-gray-900\">Ozow Payment</h1><p class=\"text-xs text-gray-500\">Secure Payment Portal</p></div></div></div><div class=\"navbar-end\"><a class=\"btn btn-primary shadow-md hover:shadow-lg transition-shadow\" href=\"/ozow/login\"><span class=\"text-sm\">🔐</span> Login</a></div></header>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

func HeaderWithUser(page string, user *models.User) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var2 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var2 == nil {
			templ_7745c5c3_Var2 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, "<header class=\"navbar bg-white border-b border-gray-200 px-4 py-3\"><div class=\"navbar-start\"><div class=\"flex items-center space-x-3\"><div class=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\"><span class=\"text-white font-bold text-sm\">💳</span></div><div><h1 class=\"text-xl font-semibold text-gray-900\">Ozow Payment</h1><p class=\"text-xs text-gray-500\">Secure Payment Portal</p></div></div></div><div class=\"navbar-center hidden lg:flex\"><div class=\"bg-green-50 border border-green-200 rounded-full px-4 py-2\"><div class=\"flex items-center space-x-2\"><div class=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div><span class=\"text-sm font-medium text-green-700\">Secure Session Active</span></div></div></div><div class=\"navbar-end\"><div class=\"flex items-center space-x-4\"><!-- User Info --><div class=\"hidden md:flex flex-col items-end\"><span class=\"text-sm font-medium text-gray-900\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var3 string
		templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(user.FirstName)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/header.templ`, Line: 54, Col: 69}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, " ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var4 string
		templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(user.Surname)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/header.templ`, Line: 54, Col: 86}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, "</span> <span class=\"text-xs text-gray-500\">ID: ")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var5 string
		templ_7745c5c3_Var5, templ_7745c5c3_Err = templ.JoinStringErrs(user.IdentificationDetail)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/header.templ`, Line: 55, Col: 72}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var5))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 5, "</span></div><!-- User Menu Dropdown --><div class=\"dropdown dropdown-end\"><div tabindex=\"0\" role=\"button\" class=\"btn btn-ghost btn-circle avatar hover:bg-gray-100\"><div class=\"w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-md\"><span class=\"text-white font-bold text-sm\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var6 string
		templ_7745c5c3_Var6, templ_7745c5c3_Err = templ.JoinStringErrs(string([]rune(user.FirstName)[0]))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/header.templ`, Line: 63, Col: 43}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var6))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var7 string
		templ_7745c5c3_Var7, templ_7745c5c3_Err = templ.JoinStringErrs(string([]rune(user.Surname)[0]))
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/views/header.templ`, Line: 63, Col: 78}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var7))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 6, "</span></div></div><ul tabindex=\"0\" class=\"mt-3 z-50 p-2 shadow-xl menu menu-sm dropdown-content bg-white rounded-xl border border-gray-200 w-56\"><li class=\"menu-title\"><span class=\"text-gray-500 font-medium\">Account</span></li><li><a class=\"flex items-center space-x-3 py-3 px-3 hover:bg-gray-50 rounded-lg\"><div class=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\"><span class=\"text-blue-600 text-sm\">👤</span></div><div><span class=\"font-medium\">Profile</span> <span class=\"text-xs text-gray-500 block\">View account details</span></div></a></li><li><a class=\"flex items-center space-x-3 py-3 px-3 hover:bg-gray-50 rounded-lg\"><div class=\"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center\"><span class=\"text-purple-600 text-sm\">📊</span></div><div><span class=\"font-medium\">Payment History</span> <span class=\"text-xs text-gray-500 block\">View past transactions</span></div></a></li><li><a class=\"flex items-center space-x-3 py-3 px-3 hover:bg-gray-50 rounded-lg\"><div class=\"w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center\"><span class=\"text-gray-600 text-sm\">⚙️</span></div><div><span class=\"font-medium\">Settings</span> <span class=\"text-xs text-gray-500 block\">Account preferences</span></div></a></li><div class=\"divider my-2\"></div><li><button hx-post=\"/ozow/logout\" hx-confirm=\"Are you sure you want to logout?\" class=\"flex items-center space-x-3 py-3 px-3 hover:bg-red-50 rounded-lg text-red-600 w-full\"><div class=\"w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center\"><span class=\"text-red-600 text-sm\">🚪</span></div><div class=\"text-left\"><span class=\"font-medium\">Logout</span> <span class=\"text-xs text-red-500 block\">End secure session</span></div></button></li></ul></div></div></div></header>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
