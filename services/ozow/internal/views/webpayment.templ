package views

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/models"
)

const (
	page         = "Pay"
	style        = "/ozow/css/style.css"
	payment_link = "/ozow/pay"
)

templ Index() {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Pay</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@components.Header(page)
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="items-center justify-center  text-center text-3xl">
						🪙
					</div>
					<div class="card-body grid place-items-center">
						<form
							id="payment-form"
							hx-post={ payment_link }
							hx-target="#toast"
							hx-indicator="#indicator"
							hx-swap="innerHTML"
							class="grid place-items-center"
						>
							<div id="toast" class="card-body place-items-center w-80">
								<h2 class="card-title pb-3">Make Payment</h2>
								<input name="idNumber" id="idNumber" type="text" placeholder="ID Number" class="input focus:outline-none"/>
								<input name="amount" id="amount" type="number" placeholder="Amount" class="input focus:outline-none"/>
							</div>
							<button
								type="submit"
								class="content-center btn btn-primary btn-wide"
							>
								Pay
							</button>
							<span id="indicator" class="icon-[line-md--loading-twotone-loop] text-2xl htmx-indicator"></span>
						</form>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ IndexWithUser(user *models.User) {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Payment - { user.FullName }</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@HeaderWithUser(page, user)

			<div class="flex min-h-screen items-center justify-center bg-base-200 p-4">
				<div class="w-full max-w-md">
					<!-- User Welcome Card -->
					<div class="card bg-base-100 shadow-xl mb-6">
						<div class="card-body text-center">
							<div class="avatar placeholder mb-4">
								<div class="bg-primary text-primary-content rounded-full w-16">
									<span class="text-xl font-bold">
										{ string([]rune(user.FirstName)[0]) }{ string([]rune(user.Surname)[0]) }
									</span>
								</div>
							</div>
							<h2 class="card-title justify-center text-2xl">Welcome, { user.FirstName }!</h2>
							<p class="text-base-content/70">{ user.FullName }</p>
							if user.Employer != "" {
								<p class="text-sm text-base-content/60">{ user.Employer }</p>
							}
						</div>
					</div>

					<!-- Payment Form Card -->
					<div class="card bg-base-100 shadow-xl">
						<div class="card-body">
							<h3 class="card-title justify-center mb-6">
								<span class="text-2xl">💳</span>
								Make Payment
							</h3>

							<form
								id="payment-form"
								hx-post={ payment_link }
								hx-target="#payment-result"
								hx-indicator="#payment-indicator"
								hx-swap="innerHTML"
								class="space-y-4"
							>
								<!-- ID Number Field -->
								<div class="form-control">
									<label class="label">
										<span class="label-text">ID Number</span>
										<span class="label-text-alt text-success">✓ Verified</span>
									</label>
									<input
										name="idNumber"
										id="idNumber"
										type="text"
										value={ user.IdentificationDetail }
										class="input input-bordered bg-base-200"
										readonly
									/>
								</div>

								<!-- Amount Field -->
								<div class="form-control">
									<label class="label">
										<span class="label-text">Payment Amount</span>
									</label>
									<div class="relative">
										<span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-base-content/60">R</span>
										<input
											name="amount"
											id="amount"
											type="number"
											step="0.01"
											min="1"
											placeholder="0.00"
											class="input input-bordered pl-8 w-full"
											required
										/>
									</div>
									<label class="label">
										<span class="label-text-alt">Minimum: R1.00</span>
									</label>
								</div>

								<!-- Payment Buttons -->
								<div class="form-control mt-6">
									<button
										type="submit"
										class="btn btn-primary btn-lg w-full"
									>
										Process Payment
									</button>
								</div>

								<div class="form-control">
									<button
										type="reset"
										class="btn btn-outline w-full"
										onclick="document.getElementById('amount').value = ''"
									>
										Clear Amount
									</button>
								</div>

								<!-- Loading Indicator -->
								<div id="payment-indicator" class="htmx-indicator text-center py-4">
									<span class="loading loading-spinner loading-md"></span>
									<p class="text-sm mt-2">Processing payment...</p>
								</div>
							</form>

							<!-- Payment Result Area -->
							<div id="payment-result" class="mt-4"></div>
						</div>
					</div>
				</div>
			</div>
		</body>
	</html>
}