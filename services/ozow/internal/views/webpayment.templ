package views

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/models"
)

const (
	page         = "Pay"
	style        = "/ozow/css/style.css"
	payment_link = "/ozow/pay"
)

templ Index() {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Pay</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@components.Header(page)
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="items-center justify-center  text-center text-3xl">
						🪙
					</div>
					<div class="card-body grid place-items-center">
						<form
							id="payment-form"
							hx-post={ payment_link }
							hx-target="#toast"
							hx-indicator="#indicator"
							hx-swap="innerHTML"
							class="grid place-items-center"
						>
							<div id="toast" class="card-body place-items-center w-80">
								<h2 class="card-title pb-3">Make Payment</h2>
								<input name="idNumber" id="idNumber" type="text" placeholder="ID Number" class="input focus:outline-none"/>
								<input name="amount" id="amount" type="number" placeholder="Amount" class="input focus:outline-none"/>
							</div>
							<button
								type="submit"
								class="content-center btn btn-primary btn-wide"
							>
								Pay
							</button>
							<span id="indicator" class="icon-[line-md--loading-twotone-loop] text-2xl htmx-indicator"></span>
						</form>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ IndexWithUser(user *models.User) {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Dashboard - Welcome { user.FullName }</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
			<script type="module" src={ models.JS }></script>
			@HeaderWithUser(page, user)

			<!-- Main Dashboard Container -->
			<div class="container mx-auto px-4 py-8">
				<div class="max-w-6xl mx-auto">
					<!-- Welcome Section -->
					<div class="mb-8">
						<div class="text-center">
							<h1 class="text-4xl font-bold text-gray-800 mb-2">
								Welcome back, { user.FirstName }! 👋
							</h1>
							<p class="text-gray-600 text-lg">Ready to make a secure payment?</p>
						</div>
					</div>

					<!-- Dashboard Grid -->
					<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
						<!-- User Profile Card -->
						<div class="lg:col-span-1">
							<div class="card bg-white shadow-xl border-0">
								<div class="card-body">
									<div class="flex items-center space-x-4 mb-6">
										<div class="avatar placeholder">
											<div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full w-16 h-16">
												<span class="text-2xl font-bold">
													{ string([]rune(user.FirstName)[0]) }{ string([]rune(user.Surname)[0]) }
												</span>
											</div>
										</div>
										<div>
											<h3 class="text-xl font-semibold text-gray-800">{ user.FullName }</h3>
											<p class="text-gray-500">Account Holder</p>
										</div>
									</div>

									<div class="divider"></div>

									<div class="space-y-3">
										<div class="flex items-center space-x-3">
											<span class="icon-[heroicons--identification-card] text-blue-500 text-xl"></span>
											<div>
												<p class="text-sm text-gray-500">ID Number</p>
												<p class="font-medium">{ user.IdentificationDetail }</p>
											</div>
										</div>

										if user.Employer != "" {
											<div class="flex items-center space-x-3">
												<span class="icon-[heroicons--building-office] text-green-500 text-xl"></span>
												<div>
													<p class="text-sm text-gray-500">Employer</p>
													<p class="font-medium">{ user.Employer }</p>
												</div>
											</div>
										}

										if user.Department != "" {
											<div class="flex items-center space-x-3">
												<span class="icon-[heroicons--user-group] text-purple-500 text-xl"></span>
												<div>
													<p class="text-sm text-gray-500">Department</p>
													<p class="font-medium">{ user.Department }</p>
												</div>
											</div>
										}

										if user.EmployeeCode != "" {
											<div class="flex items-center space-x-3">
												<span class="icon-[heroicons--hashtag] text-orange-500 text-xl"></span>
												<div>
													<p class="text-sm text-gray-500">Employee Code</p>
													<p class="font-medium">{ user.EmployeeCode }</p>
												</div>
											</div>
										}
									</div>
								</div>
							</div>
						</div>

						<!-- Payment Form Card -->
						<div class="lg:col-span-2">
							<div class="card bg-white shadow-xl border-0">
								<div class="card-body">
									<div class="flex items-center space-x-3 mb-6">
										<span class="icon-[heroicons--credit-card] text-3xl text-blue-500"></span>
										<h2 class="card-title text-2xl text-gray-800">Make a Payment</h2>
									</div>

									<form
										id="payment-form"
										hx-post={ payment_link }
										hx-target="#payment-result"
										hx-indicator="#payment-indicator"
										hx-swap="innerHTML"
										class="space-y-6"
									>
										<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
											<!-- ID Number Field -->
											<div class="form-control">
												<label class="label" for="idNumber">
													<span class="label-text font-medium text-gray-700">ID Number</span>
													<span class="label-text-alt text-green-600">✓ Verified</span>
												</label>
												<input
													name="idNumber"
													id="idNumber"
													type="text"
													value={ user.IdentificationDetail }
													class="input input-bordered bg-gray-50 cursor-not-allowed"
													readonly
													disabled
												/>
											</div>

											<!-- Amount Field -->
											<div class="form-control">
												<label class="label" for="amount">
													<span class="label-text font-medium text-gray-700">Payment Amount</span>
												</label>
												<div class="relative">
													<span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">R</span>
													<input
														name="amount"
														id="amount"
														type="number"
														step="0.01"
														min="1"
														placeholder="0.00"
														class="input input-bordered pl-8 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
														required
													/>
												</div>
												<label class="label">
													<span class="label-text-alt text-gray-500">Minimum amount: R1.00</span>
												</label>
											</div>
										</div>

										<!-- Payment Button -->
										<div class="flex flex-col sm:flex-row gap-4 pt-4">
											<button
												type="submit"
												class="btn btn-primary btn-lg flex-1 text-white shadow-lg hover:shadow-xl transition-all duration-200"
											>
												<span class="icon-[heroicons--credit-card] text-xl"></span>
												Process Payment
											</button>
											<button
												type="reset"
												class="btn btn-outline btn-lg"
												onclick="document.getElementById('amount').value = ''"
											>
												<span class="icon-[heroicons--arrow-path] text-xl"></span>
												Clear
											</button>
										</div>

										<!-- Loading Indicator -->
										<div id="payment-indicator" class="htmx-indicator flex items-center justify-center py-4">
											<span class="loading loading-spinner loading-lg text-primary"></span>
											<span class="ml-3 text-gray-600">Processing your payment...</span>
										</div>
									</form>

									<!-- Payment Result Area -->
									<div id="payment-result" class="mt-6"></div>
								</div>
							</div>
						</div>
					</div>

					<!-- Quick Actions Section -->
					<div class="mt-8">
						<div class="card bg-white shadow-xl border-0">
							<div class="card-body">
								<h3 class="card-title text-xl text-gray-800 mb-4">
									<span class="icon-[heroicons--bolt] text-yellow-500"></span>
									Quick Actions
								</h3>
								<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
									<div class="btn btn-outline btn-lg h-auto p-4 flex-col space-y-2 hover:bg-blue-50">
										<span class="icon-[heroicons--clock] text-2xl text-blue-500"></span>
										<span class="text-sm">Payment History</span>
									</div>
									<div class="btn btn-outline btn-lg h-auto p-4 flex-col space-y-2 hover:bg-green-50">
										<span class="icon-[heroicons--document-text] text-2xl text-green-500"></span>
										<span class="text-sm">Statements</span>
									</div>
									<div class="btn btn-outline btn-lg h-auto p-4 flex-col space-y-2 hover:bg-purple-50">
										<span class="icon-[heroicons--cog-6-tooth] text-2xl text-purple-500"></span>
										<span class="text-sm">Settings</span>
									</div>
									<div class="btn btn-outline btn-lg h-auto p-4 flex-col space-y-2 hover:bg-orange-50">
										<span class="icon-[heroicons--question-mark-circle] text-2xl text-orange-500"></span>
										<span class="text-sm">Help & Support</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</body>
	</html>
}